name: athar-procure

services:
  app:
    hostname: procure-app
    build:
      context: ./
      dockerfile: ./development.dockerfile
      args:
        BUNDLE_GEM__FURY__IO: ${BUNDLE_GEM__FURY__IO}
    #command: tail -f /dev/null
    #command: debug
    env_file:
      - docker-compose.env
    ports:
      - "1237:1234"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.procure.rule=Host(`procure.athar.test`)"
      - "traefik.http.routers.procure.entrypoints=web,websecure"
      - "traefik.http.services.procure.loadbalancer.server.port=3003"
      - "traefik.http.routers.procure.tls=false"

      # Health Check Configuration
#      - "traefik.http.services.procure.loadbalancer.healthcheck.path=/up"
#      - "traefik.http.services.procure.loadbalancer.healthcheck.interval=10s"
#      - "traefik.http.services.procure.loadbalancer.healthcheck.timeout=3s"
    networks:
      - athar-network
    depends_on:
      - procure-db
    volumes:
      - ./config/master.key:/rails/config/master.key
      - ./:/rails

  procure-db:
    image: postgres:17
    container_name: procure-db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: procure_athar_db
    networks:
      - athar-network
    ports:
      - "5436:5432"
    labels:
      - "traefik.enable=false"

networks:
  athar-network:
    external: true
    name: athar-backend_athar-network
