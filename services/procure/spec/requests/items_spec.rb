require 'rails_helper'

RSpec.describe "/items", type: :request do
  let(:user) { create(:user) }
  let(:token) { AtharAuth.encode_token(sub: user.id, scope: 'procure', token_type: 'session', permissions: %w[read:item create:item update:item destroy:item]) }
  let(:headers) { { "Authorization" => "Bearer #{token}" } }

  let!(:item) { create(:item, name: "Laptop", approx_price: 1500.0) }

  describe "GET /index" do
    it "returns a list of items" do
      get items_path, headers: headers
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)).to be_an(Array)
    end
  end

  describe "GET /show" do
    it "returns a single item" do
      get item_path(item), headers: headers
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)["name"]).to eq("Laptop")
    end
  end

  describe "POST /create" do
    let(:valid_attributes) {
      {
        item: {
          name: "Tablet",
          description: "Samsung Android Tablet",
          category: "Electronics",
          approx_price: 1200.0,
          vendor_name: "Samsung Vendor",
          created_by_id: user.id
        }
      }
    }

    it "creates a new item" do
      expect {
        post items_path, params: valid_attributes, headers: headers
      }.to change(Item, :count).by(1)

      expect(response).to have_http_status(:created)
    end
  end

  describe "PATCH /update" do
    it "updates an existing item" do
      patch item_path(item), params: { item: { name: "Gaming Laptop" } }, headers: headers
      expect(response).to have_http_status(:ok)
      expect(item.reload.name).to eq("Gaming Laptop")
    end
  end

  describe "DELETE /destroy" do
    it "deletes an item" do
      expect {
        delete item_path(item), headers: headers
      }.to change(Item, :count).by(-1)

      expect(response).to have_http_status(:no_content)
    end
  end
end
