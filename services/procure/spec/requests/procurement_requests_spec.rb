require "rails_helper"

RSpec.describe "/items", type: :request do
  let(:user) { create(:user) }
  let(:token) do
    AtharAuth.encode_token(
      sub: user.id,
      scope: "procure",
      token_type: "session",
      permissions: %w[read:item create:item update:item destroy:item]
    )
  end
  let(:headers) { { "Authorization" => "Bearer #{token}" } }

  let!(:item) { create(:item) }

  describe "GET /index" do
    it "returns all items" do
      get items_path, headers: headers
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body).first["id"]).to eq(item.id)
    end
  end

  describe "GET /show" do
    it "returns a specific item" do
      get item_path(item), headers: headers
      expect(response).to have_http_status(:ok)
      body = JSON.parse(response.body)
      expect(body["name"]).to eq(item.name)
    end
  end

  describe "POST /create" do
    let(:valid_params) do
      {
        item: {
          name: "Printer",
          description: "HP LaserJet",
          category: "Office",
          approx_price: 350.5,
          vendor_name: "HP Vendor",
          created_by_id: user.id
        }
      }
    end

    it "creates a new item" do
      expect {
        post items_path, params: valid_params, headers: headers
      }.to change(Item, :count).by(1)

      expect(response).to have_http_status(:created)
    end
  end

  describe "PATCH /update" do
    it "updates the item name" do
      patch item_path(item), params: { item: { name: "Updated Item Name" } }, headers: headers
      expect(response).to have_http_status(:ok)
      expect(item.reload.name).to eq("Updated Item Name")
    end
  end

  describe "DELETE /destroy" do
    it "deletes the item" do
      expect {
        delete item_path(item), headers: headers
      }.to change(Item, :count).by(-1)
      expect(response).to have_http_status(:no_content)
    end
  end
end
