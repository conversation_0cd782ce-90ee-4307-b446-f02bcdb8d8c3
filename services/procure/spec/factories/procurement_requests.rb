FactoryBot.define do
  factory :procurement_request do
    item
    requester_id { create(:user).id }
    status { "pending" }
    quantity { 1 }
    note { "Requesting for project" }
    submitted_at { Time.current }
    decided_at { nil }

    # Optional traits
    trait :approved do
      status { "approved" }
      decided_at { 1.day.from_now }
    end

    trait :rejected do
      status { "rejected" }
      decided_at { 1.day.from_now }
    end
  end
end