require "rails_helper"

RSpec.describe ProcurementRequest, type: :model do
  subject(:request) { build(:procurement_request) }

  describe "validations" do
    it { is_expected.to validate_presence_of(:status) }
    it { is_expected.to validate_inclusion_of(:status).in_array(%w[pending approved rejected canceled]) }

    it { is_expected.to validate_presence_of(:quantity) }
    it { is_expected.to validate_numericality_of(:quantity).is_greater_than(0) }

    it { is_expected.to validate_presence_of(:item_id) }
    it { is_expected.to validate_presence_of(:requester_id) }
  end

  describe "associations" do
    it { is_expected.to belong_to(:item) }
    it { is_expected.to belong_to(:requester).class_name("User").with_foreign_key(:requester_id) }
  end

  describe "scopes" do
    let!(:pending)  { create(:procurement_request, status: "pending") }
    let!(:approved) { create(:procurement_request, status: "approved") }

    it ".pending returns only pending requests" do
      expect(ProcurementRequest.pending).to include(pending)
      expect(ProcurementRequest.pending).not_to include(approved)
    end

    it ".approved returns only approved requests" do
      expect(ProcurementRequest.approved).to include(approved)
      expect(ProcurementRequest.approved).not_to include(pending)
    end

    it ".by_user returns requests by given user" do
      user = pending.requester
      expect(ProcurementRequest.by_user(user.id)).to include(pending)
    end
  end

  it "is valid with factory defaults" do
    expect(request).to be_valid
  end
end