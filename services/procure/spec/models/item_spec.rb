require 'rails_helper'

RSpec.describe Item, type: :model do
  subject(:item) { build(:item) }

  describe "validations" do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:category) }
    it { is_expected.to validate_presence_of(:approx_price) }
    it { is_expected.to validate_numericality_of(:approx_price).is_greater_than_or_equal_to(0) }
    it { is_expected.to validate_presence_of(:created_by_id) }
  end

  describe "associations" do
    it { is_expected.to belong_to(:creator).class_name("User").with_foreign_key(:created_by_id).optional }
  end

  describe ".by_category" do
    let!(:electronics_item) { create(:item, category: "Electronics") }
    let!(:furniture_item)   { create(:item, category: "Furniture") }

    it "returns items with matching category" do
      expect(Item.by_category("Electronics")).to include(electronics_item)
      expect(Item.by_category("Electronics")).not_to include(furniture_item)
    end

    it "returns all items if category is nil" do
      expect(Item.by_category(nil)).to match_array(Item.all)
    end
  end

  it "is valid with default factory attributes" do
    expect(item).to be_valid
  end
end
