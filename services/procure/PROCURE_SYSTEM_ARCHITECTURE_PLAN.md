# AtharProcure - Complete System Architecture Plan

## 🎯 Executive Summary

**AtharProcure** is a procurement request management system designed to handle internal procurement approval workflows from project-based item catalog browsing to request approval and status tracking. Built as part of the Athar EMS ecosystem, it follows full project-based multi-tenancy where both items and procurement requests are project-scoped. Global users can submit requests for any project by selecting the target project. The system focuses on internal approval processes and status tracking, with external procurement handled outside the system.

## 🏗️ System Architecture Overview

### Core Principles

- **Full Project-Based Multi-Tenancy**: Both items and procurement requests scoped by `project_id`
- **Role-Based Access Control**: Granular permissions using AtharAuth
- **Approval Workflows**: Integration with Athar::Commons approval system
- **API-First Design**: RESTful APIs with comprehensive documentation
- **Microservice Integration**: gRPC communication with other Athar services

### Technology Stack

- **Backend**: Ruby on Rails 8.0 (API-only)
- **Database**: PostgreSQL with UUID primary keys
- **Authentication**: AtharAuth gem with JWT tokens
- **Documentation**: Apipie-rails for auto-generated API docs
- **Testing**: RSpec with FactoryBot
- **Serialization**: JSONAPI-Serializer for consistent API responses

## 📊 High-Level Data Model

### Core Entities

#### 1. **User** (Inherited from AtharAuth - Project-Based Authentication)

```ruby
class User < AtharAuth::Models::User
  # Inherited: id, name, email, global, project_id, user_type, scope, permissions
  # Note: project_id set during authentication, represents user's working project context

  # Procurement-specific associations
  has_many :procurement_requests, foreign_key: :requester_id
  has_many :created_items, foreign_key: :created_by_id

  # Role-based access methods
  def procurement_manager?
    role&.name == "procurement_manager"
  end

  def procurement_officer?
    role&.name == "procurement_officer"
  end

  def finance_manager?
    role&.name == "financial_manager" # Global role
  end

  def hr_manager?
    role&.name == "hr_manager" # Global role
  end

  def global_user?
    # Inherited from AtharAuth - determines if user has global access
    super || finance_manager? || hr_manager?
  end

  def can_approve_across_projects?
    finance_manager? || hr_manager? || global_user?
  end

  # Project-scoped data access (handled by AtharAuth gem)
  def accessible_requests
    if global_user? || can_approve_across_projects?
      ProcurementRequest.all # Global users see all projects
    else
      ProcurementRequest.where(project_id: project_id) # Project-scoped users see only their project
    end
  end

  def accessible_items
    if global_user?
      Item.all # Global users can see items from all projects
    else
      Item.where(project_id: project_id) # Project-based users see only their project items
    end
  end

  # Approval system integration (leverages built-in commons gem functionality)
  def pending_approvals
    # Use built-in scope from acts_as_approvable
    ProcurementRequest.pending_approval_for(id)
  end

  def my_approval_requests
    # Get approval requests I've submitted
    requested_approvals.includes(:approvable)
  end
end
```

#### 2. **Item** (Project-Based Procurement Catalog)

```ruby
class Item < ApplicationRecord
  # Core attributes
  belongs_to :creator, class_name: "User", foreign_key: :created_by_id
  has_many :procurement_requests

  # Validations
  validates :name, presence: true
  validates :category, presence: true
  validates :approx_price, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :created_by_id, presence: true
  validates :project_id, presence: true

  # Scopes
  scope :by_category, ->(cat) { where(category: cat) if cat.present? }
  scope :for_project, ->(project_id) { where(project_id: project_id) }

  # Attributes: name, description, category (string), approx_price, specifications, project_id
  # Note: Items are project-scoped, global users can see all projects' items
end
```

#### 3. **ProcurementRequest** (Project-Scoped Employee Requests with Multi-Step Approval)

```ruby
class ProcurementRequest < ApplicationRecord
  include Athar::Commons::Models::Concerns::ActsAsApprovable

  belongs_to :item
  belongs_to :requester, class_name: "User"
  has_many :comments, as: :commentable

  # Multi-step approval workflow integration
  acts_as_approvable on_status_change: :handle_approval_status_change

  # Approval workflow associations (provided by acts_as_approvable)
  # has_one :approval_request, as: :approvable
  # approval_request has_many :approval_steps
  # approval_steps have_many :approval_actions

  # Status management (no draft status - requests are created and submitted immediately)
  enum status: {
    submitted: "submitted",
    under_review: "under_review",
    approved: "approved",
    rejected: "rejected",
    cancelled: "cancelled",
    processing: "processing",    # External procurement in progress
    delivered: "delivered"       # Item delivered and received
  }

  # Project scoping (both items and procurement requests are project-scoped)
  validates :project_id, presence: true
  validates :quantity, numericality: { greater_than: 0 }
  scope :for_project, ->(project_id) { where(project_id: project_id) }

  # Ensure item and request belong to same project
  validate :item_project_matches_request_project

  private

  def item_project_matches_request_project
    return unless item && project_id

    if item.project_id != project_id
      errors.add(:item, "must belong to the same project as the request")
    end
  end

  # Approval workflow methods
  def submit_for_approval!
    return false unless submitted?

    # Use built-in approval system method from commons gem
    if submit_for_approval(requester, project_id, build_approval_context)
      update!(status: :under_review)
      true
    else
      false
    end
  end

  # External procurement status updates
  def mark_as_processing!
    return false unless approved?
    update!(status: :processing)
  end

  def mark_as_delivered!
    return false unless processing?
    update!(status: :delivered)
  end

  # Required by approval system - use existing workflow from core service
  def approval_action
    "buy_new_item_context" # Uses context-aware workflow with amount and priority
  end

  def system_name
    "procure"
  end

  private

  def build_approval_context
    # Context for the approval workflow generation (sent to Core service)
    # This matches the required_context_keys for "buy_new_item_context" workflow
    {
      amount: (item.approx_price * quantity).to_s, # Required by workflow
      priority: determine_priority, # Required by workflow
      item_category: item.category,
      quantity: quantity,
      project_id: project_id,
      requester_role: requester.role&.name
    }
  end

  def determine_priority
    # Business logic to determine priority
    total_amount = item.approx_price * quantity

    if total_amount > 5000
      "high"
    elsif total_amount > 1000
      "medium"
    else
      "low"
    end
  end

  def handle_approval_status_change
    case approval_workflow_status
    when "approved"
      update!(status: :approved)
      # Approved requests are ready for external procurement
    when "rejected"
      update!(status: :rejected)
    end
  end
end
```

## 🔐 User Roles & Permissions

### Project-Based Authentication Model

Users **login with project context** and work within that project throughout their session. The system supports both global and project-based roles:

#### Global Roles (Cross-Project Access)

- **super_admin** (Level 50): Full system access
- **admin** (Level 40): Organization-wide authority
- **financial_manager** (Level 30): Financial oversight across all projects
- **hr_manager** (Level 30): HR management across all projects
- **hr_officer** (Level 20): HR operations across all projects
- **accountant** (Level 20): Financial tracking across all projects

#### Project-Based Roles (Project-Specific Access)

- **procurement_manager** (Level 30): Manage procurement within assigned projects
- **procurement_officer** (Level 20): Handle procurement operations within assigned projects
- **project_manager** (Level 20): Project oversight and approvals
- **supervisor** (Level 30): Project supervision and approvals
- **employee** (Level 10): Submit procurement requests within assigned projects

### Permission Structure (Aligned with Core Service)

```ruby
# Procurement-specific permissions from core service
PROCUREMENT_PERMISSIONS = [
  # Procurement Requests
  { name: "Manage Procurement Requests", action: "manage", subject_class: "ProcurementRequest" },
  { name: "Read Procurement Requests", action: "read", subject_class: "ProcurementRequest" },
  { name: "Create Procurement Request", action: "create", subject_class: "ProcurementRequest" },
  { name: "Update Procurement Request", action: "update", subject_class: "ProcurementRequest" },
  { name: "Delete Procurement Request", action: "destroy", subject_class: "ProcurementRequest" },

  # Approval Actions
  { name: "Approve Procurement", action: "approve", subject_class: "ProcurementRequest" },
  { name: "Reject Procurement", action: "reject", subject_class: "ProcurementRequest" },

  # Approval Workflow
  { name: "Read Approval Requests", action: "read", subject_class: "ApprovalRequest" },
  { name: "Approve Approval Request", action: "approve", subject_class: "ApprovalRequest" },
  { name: "Reject Approval Request", action: "reject", subject_class: "ApprovalRequest" },

  # Reporting
  { name: "Generate Procurement Reports", action: "generate", subject_class: "ProcurementReport" },
  { name: "Export Procurement Reports", action: "export", subject_class: "ProcurementReport" }
]

# Role-Permission Mapping
ROLE_PERMISSIONS = {
  "financial_manager" => ["approve", "reject", "read", "generate", "export"], # Global approval authority
  "hr_manager" => ["approve", "reject", "read"], # Global approval authority
  "procurement_manager" => ["manage", "read", "create", "update", "approve", "reject"], # Project-scoped
  "procurement_officer" => ["read", "create", "update"], # Project-scoped
  "employee" => ["read", "create"] # Project-scoped, own requests only
}
```

### Project-Based Authentication Workflow

1. **User Login**: User authenticates with specific project context (handled by AtharAuth gem)
2. **Project Context**: All operations automatically scoped to user's project
3. **Global User Access**: Financial/HR managers automatically see data across ALL projects
4. **Approval Routing**:
   - Project-based approvers: Handle requests within their assigned project
   - Global approvers (HR/Finance): Can approve requests across all projects

## 🚀 API Endpoints Overview

### Core Resource APIs

#### Items Management (Project-Based with Global Access)

```
GET    /api/items                    # List items (project-scoped for project users, all projects for global users)
POST   /api/items                    # Create new item (project_id from user context or specified)
GET    /api/items/:id                # Get item details
PATCH  /api/items/:id                # Update item
DELETE /api/items/:id                # Delete item
GET    /api/items/categories         # List item categories
GET    /api/items/by_project/:project_id  # Get items for specific project (global users only)
```

#### Procurement Requests (Project-Based with Global Access)

```
GET    /api/procurement_requests     # List requests (auto-scoped: project-based users see their project, global users see all)
POST   /api/procurement_requests     # Create new request (auto-scoped to user's project context)
GET    /api/procurement_requests/:id # Get request details
PATCH  /api/procurement_requests/:id # Update request
DELETE /api/procurement_requests/:id # Cancel request
POST   /api/procurement_requests/:id/submit    # Submit for multi-step approval workflow
POST   /api/procurement_requests/:id/approve   # Approve current step (if user is in approver_ids)
POST   /api/procurement_requests/:id/reject    # Reject current step (if user is in approver_ids)
GET    /api/procurement_requests/:id/approval  # Get approval workflow status and steps
POST   /api/procurement_requests/:id/mark_processing  # Mark as processing (external procurement started)
POST   /api/procurement_requests/:id/mark_delivered   # Mark as delivered (item received)

# Request Body for POST:
# {
#   "procurement_request": {
#     "item_id": "uuid",
#     "quantity": 5,
#     "note": "Urgent requirement",
#     "project_id": "uuid"  # Required for global users, optional for project users (defaults to user's project)
#     # status automatically set to "submitted"
#   }
# }

# Request Body for Approval Actions:
# {
#   "approval_action": {
#     "action": "approve", # or "reject" or "comment"
#     "comment": "Approved for project needs"
#   }
# }

# Query Parameters:
# ?status=submitted,approved,processing,delivered  # Filter by status
# ?requester_id=uuid   # Filter by requester (for managers)
# ?project_id=uuid     # Filter by project (for global users)
```

#### Approval System (Generated by Commons Gem)

```
GET    /api/approval_requests                    # List approval requests (all or own based on permissions)
GET    /api/approval_requests/:id                # Get approval request details
POST   /api/approval_requests/:id/approve        # Approve current step
POST   /api/approval_requests/:id/reject         # Reject current step
POST   /api/approval_requests/:id/cancel         # Cancel approval request
```

### Analytics & Reporting APIs

```
GET    /api/analytics/dashboard      # Procurement dashboard data
GET    /api/analytics/spending       # Spending analysis by project
GET    /api/analytics/status         # Request status distribution
GET    /api/reports/procurement      # Generate procurement reports
```

## 🔄 Workflow Processes

### 1. Project-Based Procurement Request Workflow

```
Employee Login (project context) → Browse Project Catalog → Create Request (auto-submitted) → Multi-Step Approval
                                                                                                      ↓
Global User Login → Browse All Projects' Catalogs → Select Project → Create Request → Multi-Step Approval
                                                                              ↓
                                                                    Step 1: Project Manager Approval
                                                                              ↓
                                                                    Step 2: Financial Approval (if high-value)
                                                                              ↓
                                                                    Approved → Processing → Delivered
```

**Detailed Multi-Step Process:**

1. **Request Creation**: Employee/Global user creates request, automatically set to "submitted" status
2. **Project Selection**: Global users must specify target project, project users default to their project
3. **Item Validation**: System ensures selected item belongs to the target project
4. **Approval Workflow**: Uses existing "buy_new_item_context" workflow from Core service
5. **Step 1 - Project Approval**:
   - **Approvers**: Project managers + procurement managers for the target project
   - **Type**: "any" (one approval needed)
   - **Scope**: Project-based managers see only their project requests
6. **Step 2 - Financial Approval** (conditional):
   - **Trigger**: High-value items (>$500 threshold from workflow)
   - **Approvers**: Financial managers + HR managers (global roles)
   - **Type**: "any" (one approval needed)
   - **Scope**: Global managers see requests across all projects
7. **External Procurement**: Approved requests handled outside system
8. **Status Tracking**: Manual status updates (processing → delivered)
9. **Final Outcome**: Complete procurement lifecycle tracking

### **Existing Approval Workflows (from Core Service)**

The system uses pre-configured approval workflows from the Core service:

#### **Context-Aware Procurement Workflow** (`buy_new_item_context`)

- **Step 1**: Project Manager Approval (always required)
- **Step 2**: Procurement Approval (if amount > 1000)
- **Step 3**: Financial Approval (if amount > 500)
- **Step 4**: Admin Approval (if priority = 'high')

**Required Context**: `amount`, `priority`
**Behavior**: Auto-approve if no steps apply
**Conditions**: Dynamic step inclusion based on amount and priority

### 2. Item Catalog Management (Project-Based)

```
Procurement Manager (project context) → Create Item → Categorization → Available for Project
Global User → Select Project → Create Item → Available for Selected Project
```

### 3. External Procurement & Status Tracking

```
Approved Requests → External Procurement Team → Vendor Selection → Purchase → Status: Processing → Delivery → Status: Delivered
```

### 4. Authentication & Access Workflow

```
User Login (with project context via AtharAuth) → Work in Project Context → Global Users see all projects automatically
```

**Project Access Rules:**

- **Global Users**: Can see data from all projects and can submit requests for any project
- **Project-Based Users**: See only data from their authenticated project context
- **Item Access**: Global users see all items, project users see only their project items
- **Request Creation**: Global users must specify target project, project users default to their project
- **Authentication**: Project context set during login (handled by AtharAuth gem)
- **Data Isolation**: Both items and procurement requests respect project context

## 🎨 Frontend Integration Points

### Dashboard Components

- **Procurement Overview**: Key metrics and pending actions
- **Request Management**: Submit and track procurement requests
- **Approval Queue**: Pending approvals for managers
- **Request Tracking**: Track procurement request status and approvals

### User Experience Flows

- **Employee Flow**: Browse project catalog → Submit request → Track status through delivery
- **Global User Flow**: Browse all catalogs → Select project → Submit request → Track status
- **Manager Flow**: Review project requests → Approve/reject → Monitor project budgets
- **Finance Flow**: Global oversight → Approval decisions → Financial reports across projects
- **Procurement Team Flow**: Process approved requests → Update status (processing/delivered)

## 📈 Integration with Athar Ecosystem

### Core Service Integration

- **User Management**: Centralized user authentication and roles
- **Project Management**: Project-based data isolation
- **Permission System**: Unified authorization across services

### Case Manager Integration

- **Shared Approval Patterns**: Similar workflow management
- **Document Handling**: Consistent file management approach
- **Comment System**: Unified commenting across services

### People Service Integration

- **Employee Data**: Staff information for procurement requests
- **Budget Integration**: Department budget tracking
- **Approval Hierarchies**: Manager-employee relationships

## 🔧 Technical Implementation Notes

### Database Design

- **UUID Primary Keys**: Consistent with ecosystem standards
- **Full Project Scoping**: Both items and procurement_requests tables include `project_id`
- **Global User Access**: Global users can access and create data for any project
- **Audit Trails**: Track all changes with timestamps and user IDs
- **Soft Deletes**: Maintain data integrity for historical records

### Performance Considerations

- **Database Indexing**: Proper indexes on project_id, status, dates
- **Caching Strategy**: Redis caching for frequently accessed data
- **Background Jobs**: Async processing for notifications and reports
- **API Pagination**: Efficient data loading for large datasets

### Security Measures

- **JWT Authentication**: Secure token-based authentication
- **Role-Based Authorization**: Granular permission checking
- **Data Encryption**: Sensitive data encryption at rest
- **Audit Logging**: Comprehensive activity logging

## 🚀 Next Steps for Implementation

1. **Phase 1**: Core models and basic CRUD operations
2. **Phase 2**: Approval workflow integration
3. **Phase 3**: Advanced features (analytics, reporting)
4. **Phase 4**: Frontend integration and user experience
5. **Phase 5**: Performance optimization and scaling

This architecture plan provides a comprehensive foundation for building a robust, scalable procurement management system that integrates seamlessly with the Athar EMS ecosystem.
