# frozen_string_literal: true

Rails.application.config.to_prepare do
  # Load required types from auth gem
  require 'athar_auth/models/types/user_role_collection_type'
  require 'athar_auth/models/types/project_type'
  require 'athar_auth/models/types/role_type'

  # Register the types with auth gem classes
  ActiveModel::Type.register(:user_role_collection, AtharAuth::Models::Types::UserRoleCollectionType)
  ActiveModel::Type.register(:project, AtharAuth::Models::Types::ProjectType)
  ActiveModel::Type.register(:role, AtharAuth::Models::Types::RoleType)

  ActiveRecord::Type.register(:user_role_collection, AtharAuth::Models::Types::UserRoleCollectionType)
  ActiveRecord::Type.register(:project, AtharAuth::Models::Types::ProjectType)
  ActiveRecord::Type.register(:role, AtharAuth::Models::Types::RoleType)
end
