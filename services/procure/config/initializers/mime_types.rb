# frozen_string_literal: true

# Register MIME types for export functionality
# These are required for Rails to properly handle export format requests

# CSV export
Mime::Type.register "text/csv", :csv unless Mime::Type.lookup_by_extension(:csv)

# PDF export  
Mime::Type.register "application/pdf", :pdf unless Mime::Type.lookup_by_extension(:pdf)

# Excel export
Mime::Type.register "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", :xlsx unless Mime::Type.lookup_by_extension(:xlsx)
