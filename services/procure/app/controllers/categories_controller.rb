# frozen_string_literal: true

class CategoriesController < ApplicationController
  before_action :authenticate_user!

  api :GET, "/categories", "List all item categories"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :project_id, String, desc: "Filter by specific project (optional for global users)"
  description <<-HTML
    Returns list of all item categories with usage statistics.<br>
    Categories are derived from items in the system.
  HTML
  def index
    return unless authorize!(:read, :item)

    @categories = categories_with_stats
    render json: @categories
  end

  api :GET, "/categories/stats", "Get category usage statistics"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :project_id, String, desc: "Filter by specific project (optional for global users)"
  description <<-HTML
    Returns detailed statistics for each category including item counts, request counts, and spending.
  HTML
  def stats
    return unless authorize!(:read, :analytics)

    @category_stats = detailed_category_stats
    render json: @category_stats
  end

  api :GET, "/categories/:name/items", "Get items in a specific category"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :name, String, required: true, desc: "Category name"
  param :project_id, String, desc: "Filter by specific project (optional for global users)"
  description <<-HTML
    Returns all items in the specified category with their details.
  HTML
  def items
    return unless authorize!(:read, :item)

    @items = items_in_category(params[:name])
    render json: @items
  end

  api :GET, "/categories/popular", "Get most popular categories"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :limit, Integer, desc: "Number of categories to return (default: 10)"
  param :project_id, String, desc: "Filter by specific project (optional for global users)"
  description <<-HTML
    Returns the most popular categories based on request frequency and spending.
  HTML
  def popular
    return unless authorize!(:read, :analytics)

    limit = params[:limit]&.to_i || 10
    @popular_categories = popular_categories_data(limit)
    render json: @popular_categories
  end

  private

  def accessible_items
    @accessible_items ||= begin
      items = current_user.accessible_items
      
      if params[:project_id].present? && current_user.global_user?
        items = items.where(project_id: params[:project_id])
      end
      
      items
    end
  end

  def accessible_requests
    @accessible_requests ||= begin
      requests = current_user.accessible_requests
      
      if params[:project_id].present? && current_user.global_user?
        requests = requests.where(project_id: params[:project_id])
      end
      
      requests
    end
  end

  def categories_with_stats
    categories = accessible_items.distinct.pluck(:category).compact.sort
    
    categories.map do |category|
      item_count = accessible_items.where(category: category).count
      request_count = accessible_requests.joins(:item)
                                        .where(items: { category: category })
                                        .count
      
      {
        name: category,
        item_count: item_count,
        request_count: request_count,
        last_used: last_used_date(category)
      }
    end
  end

  def detailed_category_stats
    categories = accessible_items.distinct.pluck(:category).compact.sort
    
    categories.map do |category|
      category_items = accessible_items.where(category: category)
      category_requests = accessible_requests.joins(:item)
                                            .where(items: { category: category })
      
      {
        name: category,
        item_count: category_items.count,
        total_requests: category_requests.count,
        pending_requests: category_requests.where(status: ["submitted", "under_review"]).count,
        approved_requests: category_requests.where(status: "approved").count,
        total_spending: category_requests.joins(:item)
                                        .where(status: ["approved", "processing", "delivered"])
                                        .sum("items.approx_price * procurement_requests.quantity"),
        average_item_price: category_items.average(:approx_price)&.round(2) || 0,
        most_requested_item: most_requested_item_in_category(category),
        last_request_date: category_requests.maximum(:created_at)
      }
    end
  end

  def items_in_category(category_name)
    accessible_items.where(category: category_name)
                   .includes(:creator)
                   .map do |item|
      {
        id: item.id,
        name: item.name,
        description: item.description,
        approx_price: item.approx_price,
        specifications: item.specifications,
        creator_name: item.creator&.name,
        created_at: item.created_at,
        request_count: accessible_requests.where(item: item).count,
        last_requested: accessible_requests.where(item: item).maximum(:created_at)
      }
    end
  end

  def popular_categories_data(limit)
    # Calculate popularity based on request frequency and total spending
    category_popularity = accessible_requests.joins(:item)
                                            .group("items.category")
                                            .select(
                                              "items.category",
                                              "COUNT(*) as request_count",
                                              "SUM(items.approx_price * procurement_requests.quantity) as total_value"
                                            )
    
    # Convert to array and calculate popularity score
    popularity_scores = category_popularity.map do |stat|
      # Popularity score: 70% weight on request count, 30% on total value
      normalized_requests = stat.request_count.to_f
      normalized_value = stat.total_value.to_f / 1000 # Normalize by dividing by 1000
      
      popularity_score = (normalized_requests * 0.7) + (normalized_value * 0.3)
      
      {
        name: stat.category,
        request_count: stat.request_count,
        total_value: stat.total_value.to_f,
        popularity_score: popularity_score.round(2),
        recent_activity: recent_activity_in_category(stat.category)
      }
    end
    
    # Sort by popularity score and take top N
    popularity_scores.sort_by { |cat| -cat[:popularity_score] }.first(limit)
  end

  def last_used_date(category)
    accessible_requests.joins(:item)
                      .where(items: { category: category })
                      .maximum(:created_at)
  end

  def most_requested_item_in_category(category)
    item_requests = accessible_requests.joins(:item)
                                      .where(items: { category: category })
                                      .group(:item_id)
                                      .count
    
    if item_requests.any?
      most_requested_item_id = item_requests.max_by { |_, count| count }.first
      item = accessible_items.find_by(id: most_requested_item_id)
      
      {
        id: item&.id,
        name: item&.name,
        request_count: item_requests[most_requested_item_id]
      }
    else
      nil
    end
  end

  def recent_activity_in_category(category)
    recent_requests = accessible_requests.joins(:item)
                                        .where(items: { category: category })
                                        .where("procurement_requests.created_at >= ?", 30.days.ago)
                                        .count
    
    {
      last_30_days: recent_requests,
      trend: calculate_trend(category)
    }
  end

  def calculate_trend(category)
    current_month = accessible_requests.joins(:item)
                                      .where(items: { category: category })
                                      .where("procurement_requests.created_at >= ?", 1.month.ago)
                                      .count
    
    previous_month = accessible_requests.joins(:item)
                                       .where(items: { category: category })
                                       .where(
                                         "procurement_requests.created_at >= ? AND procurement_requests.created_at < ?",
                                         2.months.ago,
                                         1.month.ago
                                       )
                                       .count
    
    if previous_month > 0
      change_percentage = ((current_month - previous_month).to_f / previous_month * 100).round(2)
      
      if change_percentage > 10
        "increasing"
      elsif change_percentage < -10
        "decreasing"
      else
        "stable"
      end
    else
      current_month > 0 ? "new" : "inactive"
    end
  end
end
