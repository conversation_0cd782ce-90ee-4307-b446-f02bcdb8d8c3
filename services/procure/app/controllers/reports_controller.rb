# frozen_string_literal: true

class ReportsController < ApplicationController
  before_action :authenticate_user!

  api :GET, "/reports/procurement", "Generate procurement reports"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :report_type, String, required: true, desc: "Type of report", enum: %w[summary spending requests categories projects]
  param :format, String, desc: "Report format", enum: %w[json csv], default: "json"
  param :project_id, String, desc: "Filter by specific project (optional for global users)"
  param :start_date, String, desc: "Start date for report (YYYY-MM-DD)"
  param :end_date, String, desc: "End date for report (YYYY-MM-DD)"
  param :status, String, desc: "Filter by request status"
  param :category, String, desc: "Filter by item category"
  description <<-HTML
    Generates comprehensive procurement reports in various formats.<br>
    Available report types: summary, spending, requests, categories, projects.
  HTML
  def procurement
    return unless authorize!(:read, :reports)

    case params[:report_type]
    when "summary"
      @report_data = generate_summary_report
    when "spending"
      @report_data = generate_spending_report
    when "requests"
      @report_data = generate_requests_report
    when "categories"
      @report_data = generate_categories_report
    when "projects"
      @report_data = generate_projects_report
    else
      return render json: { error: "Invalid report type" }, status: :bad_request
    end

    respond_to_format
  end

  api :GET, "/reports/export", "Export data for external analysis"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :data_type, String, required: true, desc: "Type of data to export", enum: %w[items requests analytics]
  param :format, String, desc: "Export format", enum: %w[csv json xlsx], default: "csv"
  param :project_id, String, desc: "Filter by specific project (optional for global users)"
  param :start_date, String, desc: "Start date for export (YYYY-MM-DD)"
  param :end_date, String, desc: "End date for export (YYYY-MM-DD)"
  description <<-HTML
    Exports raw data for external analysis and reporting tools.
  HTML
  def export
    return unless authorize!(:read, :reports)

    case params[:data_type]
    when "items"
      @export_data = export_items_data
    when "requests"
      @export_data = export_requests_data
    when "analytics"
      @export_data = export_analytics_data
    else
      return render json: { error: "Invalid data type" }, status: :bad_request
    end

    respond_to_export_format
  end

  private

  def filtered_requests
    @filtered_requests ||= begin
      requests = current_user.accessible_requests.includes(:item, :requester)
      
      if params[:project_id].present? && current_user.global_user?
        requests = requests.where(project_id: params[:project_id])
      end
      
      if params[:start_date].present?
        requests = requests.where("created_at >= ?", Date.parse(params[:start_date]))
      end
      
      if params[:end_date].present?
        requests = requests.where("created_at <= ?", Date.parse(params[:end_date]))
      end
      
      if params[:status].present?
        requests = requests.where(status: params[:status])
      end
      
      if params[:category].present?
        requests = requests.joins(:item).where(items: { category: params[:category] })
      end
      
      requests
    end
  end

  def generate_summary_report
    {
      report_type: "summary",
      generated_at: Time.current,
      period: report_period,
      summary: {
        total_requests: filtered_requests.count,
        total_value: calculate_total_value(filtered_requests),
        approved_requests: filtered_requests.where(status: "approved").count,
        pending_requests: filtered_requests.where(status: ["submitted", "under_review"]).count,
        average_request_value: calculate_average_value(filtered_requests),
        unique_requesters: filtered_requests.distinct.count(:requester_id),
        unique_items: filtered_requests.distinct.count(:item_id)
      },
      breakdown: {
        by_status: filtered_requests.group(:status).count,
        by_category: filtered_requests.joins(:item).group("items.category").count,
        by_month: filtered_requests.group_by_month(:created_at).count
      }
    }
  end

  def generate_spending_report
    {
      report_type: "spending",
      generated_at: Time.current,
      period: report_period,
      total_spending: calculate_total_value(filtered_requests.where(status: ["approved", "processing", "delivered"])),
      spending_breakdown: {
        by_project: spending_by_project,
        by_category: spending_by_category,
        by_month: spending_by_month,
        by_requester: spending_by_requester
      },
      top_spenders: top_spending_analysis,
      budget_analysis: budget_analysis
    }
  end

  def generate_requests_report
    {
      report_type: "requests",
      generated_at: Time.current,
      period: report_period,
      requests: filtered_requests.map do |request|
        {
          id: request.id,
          item_name: request.item.name,
          category: request.item.category,
          requester_name: request.requester.name,
          quantity: request.quantity,
          unit_price: request.item.approx_price,
          total_value: request.item.approx_price * request.quantity,
          status: request.status,
          created_at: request.created_at,
          submitted_at: request.submitted_at,
          project_id: request.project_id
        }
      end,
      summary: {
        total_count: filtered_requests.count,
        total_value: calculate_total_value(filtered_requests)
      }
    }
  end

  def generate_categories_report
    categories = filtered_requests.joins(:item).distinct.pluck("items.category").compact
    
    {
      report_type: "categories",
      generated_at: Time.current,
      period: report_period,
      categories: categories.map do |category|
        category_requests = filtered_requests.joins(:item).where(items: { category: category })
        
        {
          name: category,
          request_count: category_requests.count,
          total_value: calculate_total_value(category_requests),
          average_value: calculate_average_value(category_requests),
          status_breakdown: category_requests.group(:status).count,
          top_items: top_items_in_category(category)
        }
      end
    }
  end

  def generate_projects_report
    return { error: "Project reports only available for global users" } unless current_user.global_user?
    
    projects = filtered_requests.distinct.pluck(:project_id).compact
    
    {
      report_type: "projects",
      generated_at: Time.current,
      period: report_period,
      projects: projects.map do |project_id|
        project_requests = filtered_requests.where(project_id: project_id)
        
        {
          project_id: project_id,
          request_count: project_requests.count,
          total_value: calculate_total_value(project_requests),
          status_breakdown: project_requests.group(:status).count,
          category_breakdown: project_requests.joins(:item).group("items.category").count,
          top_requesters: top_requesters_in_project(project_id)
        }
      end
    }
  end

  def export_items_data
    current_user.accessible_items.map do |item|
      {
        id: item.id,
        name: item.name,
        description: item.description,
        category: item.category,
        approx_price: item.approx_price,
        specifications: item.specifications,
        project_id: item.project_id,
        created_at: item.created_at,
        creator_name: item.creator&.name
      }
    end
  end

  def export_requests_data
    filtered_requests.map do |request|
      {
        id: request.id,
        item_id: request.item.id,
        item_name: request.item.name,
        category: request.item.category,
        requester_id: request.requester.id,
        requester_name: request.requester.name,
        quantity: request.quantity,
        unit_price: request.item.approx_price,
        total_value: request.item.approx_price * request.quantity,
        status: request.status,
        project_id: request.project_id,
        created_at: request.created_at,
        submitted_at: request.submitted_at,
        updated_at: request.updated_at
      }
    end
  end

  def export_analytics_data
    {
      summary: generate_summary_report[:summary],
      spending: generate_spending_report[:spending_breakdown],
      categories: generate_categories_report[:categories]
    }
  end

  def respond_to_format
    case params[:format]
    when "csv"
      render plain: convert_to_csv(@report_data), content_type: "text/csv"
    else
      render json: @report_data
    end
  end

  def respond_to_export_format
    case params[:format]
    when "csv"
      render plain: convert_to_csv(@export_data), content_type: "text/csv"
    else
      render json: @export_data
    end
  end

  def convert_to_csv(data)
    # Simple CSV conversion - in production, use a proper CSV library
    if data.is_a?(Array) && data.first.is_a?(Hash)
      headers = data.first.keys.join(",")
      rows = data.map { |row| row.values.join(",") }
      ([headers] + rows).join("\n")
    else
      "Complex data structure - JSON format recommended"
    end
  end

  def calculate_total_value(requests)
    requests.joins(:item).sum("items.approx_price * procurement_requests.quantity")
  end

  def calculate_average_value(requests)
    total = calculate_total_value(requests)
    count = requests.count
    count > 0 ? (total / count).round(2) : 0
  end

  def spending_by_project
    if current_user.global_user?
      filtered_requests.joins(:item)
                       .where(status: ["approved", "processing", "delivered"])
                       .group(:project_id)
                       .sum("items.approx_price * procurement_requests.quantity")
    else
      { current_user.project_id => calculate_total_value(filtered_requests.where(status: ["approved", "processing", "delivered"])) }
    end
  end

  def spending_by_category
    filtered_requests.joins(:item)
                     .where(status: ["approved", "processing", "delivered"])
                     .group("items.category")
                     .sum("items.approx_price * procurement_requests.quantity")
  end

  def spending_by_month
    filtered_requests.joins(:item)
                     .where(status: ["approved", "processing", "delivered"])
                     .group_by_month(:created_at)
                     .sum("items.approx_price * procurement_requests.quantity")
  end

  def spending_by_requester
    filtered_requests.joins(:item, :requester)
                     .where(status: ["approved", "processing", "delivered"])
                     .group("users.name")
                     .sum("items.approx_price * procurement_requests.quantity")
  end

  def top_spending_analysis
    spending_by_requester.sort_by { |_, value| -value }.first(10).to_h
  end

  def budget_analysis
    # Placeholder - would integrate with budget service
    {
      allocated_budget: 100000,
      spent_budget: calculate_total_value(filtered_requests.where(status: ["approved", "processing", "delivered"])),
      remaining_budget: 100000 - calculate_total_value(filtered_requests.where(status: ["approved", "processing", "delivered"]))
    }
  end

  def top_items_in_category(category)
    filtered_requests.joins(:item)
                     .where(items: { category: category })
                     .group(:item_id)
                     .group("items.name")
                     .count
                     .sort_by { |_, count| -count }
                     .first(5)
                     .map { |(item_id, name), count| { item_id: item_id, name: name, request_count: count } }
  end

  def top_requesters_in_project(project_id)
    filtered_requests.joins(:requester)
                     .where(project_id: project_id)
                     .group("users.name")
                     .count
                     .sort_by { |_, count| -count }
                     .first(5)
                     .to_h
  end

  def report_period
    start_date = params[:start_date] || filtered_requests.minimum(:created_at)&.to_date
    end_date = params[:end_date] || filtered_requests.maximum(:created_at)&.to_date
    
    {
      start_date: start_date,
      end_date: end_date
    }
  end
end
