class ProcurementRequestsController < ApplicationController
  before_action :authenticate_session!
  before_action :set_procurement_request, only: %i[show update destroy approve reject mark_processing mark_delivered]

  api :GET, "/procurement_requests", "Lists all procurement requests"
  header "Authorization", "Scoped session token as Bearer token", required: true
  description <<-HTML
    Returns a list of procurement requests.<br>
    Requires permission: <code>:read, :procurement_request</code>.
  HTML
  def index
    return unless authorize!(:read, :procurement_request)

    @procurement_requests = current_user.accessible_requests
    render json: @procurement_requests
  end

  api :GET, "/procurement_requests/:id", "Retrieves a specific request"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the request"
  description <<-HTML
    Fetches a specific procurement request.<br>
    Requires permission: <code>:read, :procurement_request</code>.
  HTML
  def show
    return unless authorize!(:read, :procurement_request)

    render json: @procurement_request
  end

  api :POST, "/procurement_requests", "Creates a new procurement request"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :procurement_request, Hash, required: true, desc: "Request details" do
    param :item_id, String, required: true, desc: "ID of the item being requested"
    param :quantity, Integer, required: true, desc: "Quantity requested"
    param :note, String, desc: "Optional employee note"
    param :project_id, String, desc: "Project ID (required for global users, optional for project users)"
  end
  description <<-HTML
    Creates a new procurement request.<br>
    Requires permission: <code>:create, :procurement_request</code>.
  HTML
  def create
    return unless authorize!(:create, :procurement_request)

    # Determine project_id: use provided project_id for global users, default to user's project for project users
    target_project_id = determine_target_project_id

    @procurement_request = ProcurementRequest.new(
      procurement_request_params.merge(
        status: "submitted",
        project_id: target_project_id,
        requester_id: current_user.id,
        submitted_at: Time.current
      )
    )

    if @procurement_request.save
      # Approval workflow automatically triggered via after_create callback
      render json: @procurement_request, status: :created
    else
      render json: @procurement_request.errors, status: :unprocessable_entity
    end
  end

  api :PUT, "/procurement_requests/:id", "Updates a procurement request"
  api :PATCH, "/procurement_requests/:id", "Partially updates a procurement request"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the request"
  description <<-HTML
    Updates an existing request.<br>
    Requires permission: <code>:update, :procurement_request</code>.
  HTML
  def update
    return unless authorize!(:update, :procurement_request)

    if @procurement_request.update(procurement_request_params)
      render json: @procurement_request
    else
      render json: @procurement_request.errors, status: :unprocessable_entity
    end
  end

  api :DELETE, "/procurement_requests/:id", "Deletes a request"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the request"
  description <<-HTML
    Deletes a procurement request.<br>
    Requires permission: <code>:destroy, :procurement_request</code>.
  HTML
  def destroy
    return unless authorize!(:destroy, :procurement_request)

    @procurement_request.destroy!
  end

  api :POST, "/procurement_requests/:id/approve", "Approve current step"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the request"
  param :approval_action, Hash, required: true, desc: "Approval action details" do
    param :comment, String, desc: "Optional approval comment"
  end
  def approve
    return unless authorize!(:approve, :procurement_request)

    result = @procurement_request.approval_approve!(
      current_user.id,
      comment: params.dig(:approval_action, :comment)
    )

    if result.success?
      render json: { message: "Request approved successfully" }
    else
      render json: { error: result.message }, status: :unprocessable_entity
    end
  end

  api :POST, "/procurement_requests/:id/reject", "Reject current step"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the request"
  param :approval_action, Hash, required: true, desc: "Approval action details" do
    param :comment, String, desc: "Optional rejection comment"
  end
  def reject
    return unless authorize!(:reject, :procurement_request)

    result = @procurement_request.approval_reject!(
      current_user.id,
      comment: params.dig(:approval_action, :comment)
    )

    if result.success?
      render json: { message: "Request rejected successfully" }
    else
      render json: { error: result.message }, status: :unprocessable_entity
    end
  end

  api :POST, "/procurement_requests/:id/mark_processing", "Mark as processing"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the request"
  def mark_processing
    return unless authorize!(:update, :procurement_request)

    if @procurement_request.mark_as_processing!
      render json: { message: "Request marked as processing" }
    else
      render json: { error: "Cannot mark as processing" }, status: :unprocessable_entity
    end
  end

  api :POST, "/procurement_requests/:id/mark_delivered", "Mark as delivered"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the request"
  def mark_delivered
    return unless authorize!(:update, :procurement_request)

    if @procurement_request.mark_as_delivered!
      render json: { message: "Request marked as delivered" }
    else
      render json: { error: "Cannot mark as delivered" }, status: :unprocessable_entity
    end
  end

  api :GET, "/procurement_requests/:id/approval", "Get approval workflow status"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the request"
  def approval
    return unless authorize!(:read, :procurement_request)

    if @procurement_request.approval_request
      render json: {
        approval_request: @procurement_request.approval_request,
        current_step: @procurement_request.current_approval_step,
        status: @procurement_request.approval_workflow_status
      }
    else
      render json: { message: "No approval workflow found" }, status: :not_found
    end
  end

  private

  def set_procurement_request
    @procurement_request = ProcurementRequest.find(params[:id])
  end

  def procurement_request_params
    params.require(:procurement_request).permit(:item_id, :quantity, :note, :project_id)
  end

  def determine_target_project_id
    if current_user.global_user?
      # Global users must specify project_id
      project_id = params.dig(:procurement_request, :project_id)
      if project_id.blank?
        raise ActionController::ParameterMissing, "project_id is required for global users"
      end
      project_id
    else
      # Project users default to their project
      params.dig(:procurement_request, :project_id) || current_user.project_id
    end
  end
end
