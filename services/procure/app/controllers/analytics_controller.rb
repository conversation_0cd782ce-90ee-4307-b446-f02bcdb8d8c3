# frozen_string_literal: true

class AnalyticsController < ApplicationController
  before_action :authenticate_user!

  api :GET, "/analytics/dashboard", "Get procurement dashboard data"
  header "Authorization", "Scoped session token as Bearer token", required: true
  description <<-HTML
    Returns dashboard analytics including request counts, spending summaries, and status distribution.<br>
    Data is scoped based on user permissions (project-based or global access).
  HTML
  def dashboard
    return unless authorize!(:read, :analytics)

    @dashboard_data = {
      summary: dashboard_summary,
      recent_requests: recent_requests_data,
      spending_by_project: spending_by_project_data,
      status_distribution: status_distribution_data,
      top_categories: top_categories_data
    }

    render json: @dashboard_data
  end

  api :GET, "/analytics/spending", "Get spending analysis by project"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :project_id, String, desc: "Filter by specific project (optional for global users)"
  param :start_date, String, desc: "Start date for analysis (YYYY-MM-DD)"
  param :end_date, String, desc: "End date for analysis (YYYY-MM-DD)"
  param :group_by, String, desc: "Group by: month, quarter, year", enum: %w[month quarter year]
  description <<-HTML
    Returns detailed spending analysis with breakdown by projects, categories, and time periods.
  HTML
  def spending
    return unless authorize!(:read, :analytics)

    @spending_data = {
      total_spending: total_spending,
      spending_by_project: spending_by_project_detailed,
      spending_by_category: spending_by_category,
      spending_over_time: spending_over_time,
      budget_utilization: budget_utilization
    }

    render json: @spending_data
  end

  api :GET, "/analytics/status", "Get request status distribution"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :project_id, String, desc: "Filter by specific project (optional for global users)"
  param :start_date, String, desc: "Start date for analysis (YYYY-MM-DD)"
  param :end_date, String, desc: "End date for analysis (YYYY-MM-DD)"
  description <<-HTML
    Returns status distribution of procurement requests with counts and percentages.
  HTML
  def status
    return unless authorize!(:read, :analytics)

    @status_data = {
      status_counts: status_counts_data,
      status_trends: status_trends_data,
      average_approval_time: average_approval_time_data,
      bottlenecks: approval_bottlenecks_data
    }

    render json: @status_data
  end

  private

  def accessible_requests
    @accessible_requests ||= current_user.accessible_requests.includes(:item, :requester)
  end

  def filtered_requests
    @filtered_requests ||= begin
      requests = accessible_requests
      
      if params[:project_id].present? && current_user.global_user?
        requests = requests.where(project_id: params[:project_id])
      end
      
      if params[:start_date].present?
        requests = requests.where("created_at >= ?", Date.parse(params[:start_date]))
      end
      
      if params[:end_date].present?
        requests = requests.where("created_at <= ?", Date.parse(params[:end_date]))
      end
      
      requests
    end
  end

  def dashboard_summary
    {
      total_requests: accessible_requests.count,
      pending_requests: accessible_requests.where(status: ["submitted", "under_review"]).count,
      approved_requests: accessible_requests.where(status: "approved").count,
      total_value: calculate_total_value(accessible_requests),
      this_month_requests: accessible_requests.where("created_at >= ?", 1.month.ago).count
    }
  end

  def recent_requests_data
    accessible_requests.order(created_at: :desc)
                      .limit(10)
                      .map do |request|
      {
        id: request.id,
        item_name: request.item.name,
        requester_name: request.requester.name,
        status: request.status,
        total_value: request.item.approx_price * request.quantity,
        created_at: request.created_at
      }
    end
  end

  def spending_by_project_data
    if current_user.global_user?
      accessible_requests.joins(:item)
                         .group(:project_id)
                         .sum("items.approx_price * procurement_requests.quantity")
    else
      { current_user.project_id => calculate_total_value(accessible_requests) }
    end
  end

  def status_distribution_data
    accessible_requests.group(:status).count
  end

  def top_categories_data
    accessible_requests.joins(:item)
                      .group("items.category")
                      .sum("items.approx_price * procurement_requests.quantity")
                      .sort_by { |_, value| -value }
                      .first(5)
                      .to_h
  end

  def total_spending
    calculate_total_value(filtered_requests.where(status: ["approved", "processing", "delivered"]))
  end

  def spending_by_project_detailed
    if current_user.global_user?
      filtered_requests.joins(:item)
                       .where(status: ["approved", "processing", "delivered"])
                       .group(:project_id)
                       .group("items.category")
                       .sum("items.approx_price * procurement_requests.quantity")
    else
      filtered_requests.joins(:item)
                       .where(status: ["approved", "processing", "delivered"])
                       .group("items.category")
                       .sum("items.approx_price * procurement_requests.quantity")
    end
  end

  def spending_by_category
    filtered_requests.joins(:item)
                     .where(status: ["approved", "processing", "delivered"])
                     .group("items.category")
                     .sum("items.approx_price * procurement_requests.quantity")
  end

  def spending_over_time
    group_by = params[:group_by] || "month"
    
    case group_by
    when "month"
      filtered_requests.joins(:item)
                       .where(status: ["approved", "processing", "delivered"])
                       .group_by_month(:created_at)
                       .sum("items.approx_price * procurement_requests.quantity")
    when "quarter"
      filtered_requests.joins(:item)
                       .where(status: ["approved", "processing", "delivered"])
                       .group_by_quarter(:created_at)
                       .sum("items.approx_price * procurement_requests.quantity")
    when "year"
      filtered_requests.joins(:item)
                       .where(status: ["approved", "processing", "delivered"])
                       .group_by_year(:created_at)
                       .sum("items.approx_price * procurement_requests.quantity")
    end
  end

  def budget_utilization
    # This would require budget data from another service
    # For now, return placeholder
    {
      allocated_budget: 100000,
      spent_budget: total_spending,
      utilization_percentage: total_spending > 0 ? (total_spending / 100000.0 * 100).round(2) : 0
    }
  end

  def status_counts_data
    filtered_requests.group(:status).count
  end

  def status_trends_data
    filtered_requests.group_by_month(:created_at)
                     .group(:status)
                     .count
  end

  def average_approval_time_data
    # Calculate average time from submitted to approved
    approved_requests = filtered_requests.where(status: ["approved", "processing", "delivered"])
    
    if approved_requests.any?
      total_time = approved_requests.sum do |request|
        # This would need approval timestamps from approval system
        # For now, use created_at to updated_at as approximation
        (request.updated_at - request.created_at).to_i
      end
      
      (total_time / approved_requests.count / 1.day).round(2)
    else
      0
    end
  end

  def approval_bottlenecks_data
    # This would analyze approval step durations
    # For now, return status-based analysis
    status_counts = status_counts_data
    total_requests = status_counts.values.sum
    
    {
      pending_approval_percentage: status_counts["under_review"].to_f / total_requests * 100,
      stuck_requests: filtered_requests.where(status: "under_review")
                                      .where("created_at < ?", 7.days.ago)
                                      .count
    }
  end

  def calculate_total_value(requests)
    requests.joins(:item).sum("items.approx_price * procurement_requests.quantity")
  end
end
