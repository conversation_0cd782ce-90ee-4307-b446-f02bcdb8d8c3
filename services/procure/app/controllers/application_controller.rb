class ApplicationController < ActionController::API
  include ::AtharAuth::ControllerHelpers
  include ::AtharAuth::Authorization

  before_action :authenticate_api_user!
  # before_action :configure_permitted_parameters, if: :devise_controller?

  protected

  def configure_permitted_parameters
    # devise_parameter_sanitizer.permit(:sign_up, keys: %i[name avatar])
    # devise_parameter_sanitizer.permit(:account_update, keys: %i[name avatar])
  end
end
