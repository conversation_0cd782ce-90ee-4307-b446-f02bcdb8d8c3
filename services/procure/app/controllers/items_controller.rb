class ItemsController < ApplicationController
  before_action :authenticate_session!
  before_action :set_item, only: %i[show update destroy]

  api :GET, "/items", "Lists all predefined procurement items"
  header "Authorization", "Scoped session token as Bearer token", required: true
  description <<-HTML
    Returns a list of predefined items that can be requested by employees.<br>
    Only Project Managers are allowed to manage these items.<br>
    Requires permission: <code>:read, :item</code>.
  HTML
  returns code: 200, desc: "List of items"

  def index
    return unless authorize!(:read, :item)

    @items = current_user.accessible_items
    render json: @items
  end

  api :GET, "/items/:id", "Retrieves a specific item"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the item"
  description <<-HTML
    Fetches detailed information about a predefined procurement item.<br>
    Requires permission: <code>:read, :item</code>.
  HTML
  returns code: 200, desc: "Item details"

  def show
    return unless authorize!(:read, :item)

    render json: @item
  end

  api :POST, "/items", "Creates a new procurement item"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :item, Hash, required: true, desc: "Item attributes" do
    param :name, String, required: true, desc: "Item name"
    param :description, String, required: false, desc: "Item description"
    param :category, String, required: true, desc: "Category of the item (e.g., electronics)"
    param :approx_price, Float, required: true, desc: "Estimated price"
    param :project_id, String, desc: "Project ID (required for global users, optional for project users)"
  end
  description <<-HTML
    Creates a new predefined item in the system.<br>
    Only Project Managers are authorized to create items.<br>
    Requires permission: <code>:create, :item</code>.
  HTML
  returns code: 201, desc: "Item created successfully"
  error code: 422, desc: "Validation errors"

  def create
    return unless authorize!(:create, :item)

    # Determine project_id: use provided project_id for global users, default to user's project for project users
    target_project_id = determine_target_project_id

    @item = Item.new(
      item_params.merge(
        project_id: target_project_id,
        created_by_id: current_user.id
      )
    )

    if @item.save
      render json: @item, status: :created, location: @item
    else
      render json: @item.errors, status: :unprocessable_entity
    end
  end

  api :PUT, "/items/:id", "Updates a procurement item"
  api :PATCH, "/items/:id", "Partially updates a procurement item"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the item"
  param :item, Hash, required: true, desc: "Item fields to update" do
    param :name, String, desc: "Item name"
    param :description, String, desc: "Item description"
    param :category, String, desc: "Category"
    param :approx_price, Float, desc: "Estimated price"
    param :project_id, String, desc: "Project ID"
  end
  description <<-HTML
    Updates an existing item.<br>
    Only Project Managers can update items.<br>
    Requires permission: <code>:update, :item</code>.
  HTML
  returns code: 200, desc: "Item updated successfully"
  error code: 422, desc: "Validation errors"

  def update
    return unless authorize!(:update, :item)

    if @item.update(item_params)
      render json: @item
    else
      render json: @item.errors, status: :unprocessable_entity
    end
  end

  api :DELETE, "/items/:id", "Deletes a procurement item"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the item"
  description <<-HTML
    Deletes a predefined procurement item.<br>
    Only Project Managers can delete items.<br>
    Requires permission: <code>:destroy, :item</code>.
  HTML
  returns code: 204, desc: "Item deleted successfully"

  def destroy
    return unless authorize!(:destroy, :item)

    @item.destroy!
  end

  api :GET, "/items/by_project/:project_id", "Get items for specific project"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :project_id, String, required: true, desc: "Project ID"
  description <<-HTML
    Gets items for a specific project (global users only).<br>
    Requires permission: <code>:read, :item</code>.
  HTML
  def by_project
    return unless authorize!(:read, :item)
    return unless current_user.global_user?

    @items = Item.for_project(params[:project_id])
    render json: @items
  end

  private

  def set_item
    @item = Item.find(params[:id])
  end

  def item_params
    params.require(:item).permit(:name, :description, :category, :approx_price, :project_id)
  end

  def determine_target_project_id
    if current_user.global_user?
      # Global users must specify project_id
      project_id = params.dig(:item, :project_id)
      if project_id.blank?
        raise ActionController::ParameterMissing, "project_id is required for global users"
      end
      project_id
    else
      # Project users default to their project
      params.dig(:item, :project_id) || current_user.project_id
    end
  end
end
