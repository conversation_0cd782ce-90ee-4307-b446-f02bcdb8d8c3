class ProcurementRequest < ApplicationRecord
  include Athar::Commons::Models::Concerns::ActsAsApprovable

  belongs_to :item
  belongs_to :requester, class_name: "User", foreign_key: :requester_id

  # Multi-step approval workflow integration
  acts_as_approvable on_status_change: :handle_approval_status_change

  # Status management (no draft status - requests are created and submitted immediately)
  # Status values: submitted, under_review, approved, rejected, cancelled, processing, delivered
  enum :status, {
    submitted: "submitted",
    under_review: "under_review",
    approved: "approved",
    rejected: "rejected",
    cancelled: "cancelled",
    processing: "processing",
    delivered: "delivered"
  }

  # Project scoping (both items and procurement requests are project-scoped)
  validates :project_id, presence: true
  validates :quantity, numericality: { greater_than: 0 }
  scope :for_project, ->(project_id) { where(project_id: project_id) }

  # Ensure item and request belong to same project
  validate :item_project_matches_request_project

  # Scopes
  scope :pending, -> { where(status: "submitted") }
  scope :approved, -> { where(status: "approved") }
  scope :by_user, ->(user_id) { where(requester_id: user_id) }

  # Trigger approval workflow automatically on creation
  after_create :trigger_approval_workflow

  # External procurement status updates
  def mark_as_processing!
    return false unless approved?
    update!(status: :processing)
  end

  def mark_as_delivered!
    return false unless processing?
    update!(status: :delivered)
  end

  # Required by approval system - use existing workflow from core service
  def approval_action
    "buy_new_item_context" # Uses context-aware workflow with amount and priority
  end

  def system_name
    "procure"
  end

  private

  def build_approval_context
    # Context for the approval workflow generation (sent to Core service)
    # This matches the required_context_keys for "buy_new_item_context" workflow
    {
      amount: (item.approx_price * quantity).to_s, # Required by workflow
      priority: determine_priority, # Required by workflow
      item_category: item.category,
      quantity: quantity,
      project_id: project_id,
      requester_role: requester.role&.name
    }
  end

  def determine_priority
    # Business logic to determine priority
    total_amount = item.approx_price * quantity

    if total_amount > 5000
      "high"
    elsif total_amount > 1000
      "medium"
    else
      "low"
    end
  end

  def handle_approval_status_change
    case approval_workflow_status
    when "approved"
      update!(status: :approved)
      # Approved requests are ready for external procurement
    when "rejected"
      update!(status: :rejected)
    end
  end

  private

  def trigger_approval_workflow
    if submit_for_approval(requester, project_id, build_approval_context)
      update!(status: :under_review)
    end
  end

  def item_project_matches_request_project
    return unless item && project_id

    if item.project_id != project_id
      errors.add(:item, "must belong to the same project as the request")
    end
  end
end
