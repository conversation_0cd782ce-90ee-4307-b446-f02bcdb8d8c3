class ApprovalRequest < ApplicationRecord
  include Athar::Commons::Models::Concerns::Ransackable

  # Requestor is referenced by ID but not through a foreign key
  attr_accessor :requestor
  # Project is referenced by ID but not through a foreign key
  attr_accessor :project
  belongs_to :approvable, polymorphic: true

  has_many :approval_steps, dependent: :destroy
  has_many :approval_actions, dependent: :destroy

  include Athar::Commons::Models::Concerns::ApprovalOperations

  enum :status, { pending: 0, approved: 1, rejected: 2, canceled: 3 }

  validates :workflow_id, presence: true
  validates :workflow_name, presence: true
  validates :requestor_id, presence: true
  validates :status, presence: true

  after_create :create_steps_from_data

  # Helper method to load requestor from Core service
  def load_requestor
    # This would use an RPC client to fetch the user from Core
    client = Core::UserClient.new
    response = client.get_user(requestor_id)
    @requestor = response if response
  end
  # Helper method to load project from Core service
  def load_project
    return unless project_id
    # This would use an RPC client to fetch the project from Core
    client = Core::ProjectClient.new
    response = client.get_project(project_id)
    @project = response if response
  end
  private

  def create_steps_from_data
    return unless steps_data.is_a?(Array)

    steps_data.each do |step_data|
      approval_steps.create!(
        step_id: step_data['step_id'],
        name: step_data['name'],
        sequence: step_data['sequence'],
        approval_type: step_data['approval_type'],
        approver_ids: step_data['approver_ids']
      )
    end
  end
end
