module ActiveStorage
  module Ransack<PERSON><PERSON>ttachment
    extend ActiveSupport::Concern

    included do
      # Add custom ransackers for blob attributes
      ransacker :blob_filename do
        Arel.sql('active_storage_blobs.filename')
      end

      ransacker :blob_content_type do
        Arel.sql('active_storage_blobs.content_type')
      end
    end

    class_methods do
      def ransackable_attributes(auth_object = nil)
        # Define which attributes can be searched/filtered
        %w[id created_at blob_filename blob_content_type]
      end

      def ransackable_associations(auth_object = nil)
        # Define which associations can be searched through
        %w[blob record]
      end
    end
  end
end
