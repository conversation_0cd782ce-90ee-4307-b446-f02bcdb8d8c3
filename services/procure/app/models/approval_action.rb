class ApprovalAction < ApplicationRecord
  include Athar::Commons::Models::Concerns::ApprovalActionCallbacks

  belongs_to :approval_request
  belongs_to :approval_step

  validates :user_id, presence: true
  validates :action, presence: true, inclusion: { in: %w[approve reject comment] }
  validates :user_id, uniqueness: { scope: [ :approval_request_id, :approval_step_id, :action ],
                                    message: "has already performed this action" },
            if: -> { %w[approve reject].include?(action) }
end
