class Item < ApplicationRecord
  # Core attributes
  belongs_to :creator, class_name: "User", foreign_key: :created_by_id, optional: true
  has_many :procurement_requests

  # Validations
  validates :name, presence: true
  validates :category, presence: true
  validates :approx_price, presence: true, numericality: { greater_than_or_equal_to: 0 }
  # validates :created_by_id, presence: true  # Commented out for now due to User model issues
  validates :project_id, presence: true

  # Scopes
  scope :by_category, ->(cat) { where(category: cat) if cat.present? }
  scope :for_project, ->(project_id) { where(project_id: project_id) }

  # Attributes: name, description, category (string), approx_price, specifications, project_id
  # Note: Items are project-scoped, global users can see all projects' items
end
