# frozen_string_literal: true

class User < AtharAuth::Models::User
  # Inherit all common attributes from AtharAuth::Models::User:
  # - id, name, email, global, project_id, user_type, scope, permissions
  # - global_user?, project_based_user?, can?(permission)
  # - from_token_data factory method with rich associations

  # Procurement-specific associations
  has_many :procurement_requests, foreign_key: :requester_id
  has_many :created_items, class_name: "Item", foreign_key: :created_by_id

  # Approval system associations (through commons gem)
  has_many :approval_actions, foreign_key: :user_id, class_name: "ApprovalAction"
  has_many :requested_approvals, foreign_key: :requestor_id, class_name: "ApprovalRequest"

  # Project-scoped data access (handled by AtharAuth gem)
  def accessible_requests
    if global_user? || can_approve_across_projects?
      ProcurementRequest.all # Global users see all projects
    else
      ProcurementRequest.where(project_id: project_id) # Project-scoped users see only their project
    end
  end

  def accessible_items
    if global_user?
      Item.all # Global users can see items from all projects
    else
      Item.where(project_id: project_id) # Project-based users see only their project items
    end
  end

  def can_approve_request?(request)
    return true if global_user?
    return true if procurement_manager? && request.project_id == project_id
    false
  end

  # Get procurement requests where this user can approve current step (uses built-in scope)
  def pending_approvals
    ProcurementRequest.pending_approval_for(id)
  end

  def my_approval_requests
    # Get approval requests I've submitted
    requested_approvals.includes(:approvable)
  end



  def can_approve_across_projects?
    finance_manager? || hr_manager? || global_user?
  end

  # Procure-specific role checks
  def procurement_manager?
    role&.name&.include?("procurement_manager")
  end

  def procurement_officer?
    role&.name&.include?("procurement_officer")
  end

  def finance_manager?
    role&.name == "financial_manager" # Global role
  end

  def hr_manager?
    role&.name == "hr_manager" # Global role
  end

  # Use inherited factory method from AtharAuth::Models::User
  # No need to override - from_token_data handles everything with rich associations

  private
end
