#!/usr/bin/env ruby

# Test script to verify the implementation matches the architecture plan

puts "Testing AtharProcure Implementation..."
puts "=" * 50

# Test Item model
puts "\n1. Testing Item Model:"
puts "- Creating item without project_id should fail"
item = Item.new(name: "Test Item", category: "electronics", approx_price: 100)
puts "  Item valid without project_id: #{item.valid?}"
puts "  Errors: #{item.errors.full_messages}"

# Test ProcurementRequest model
puts "\n2. Testing ProcurementRequest Model:"
puts "- Available statuses:"
puts "  #{ProcurementRequest.statuses.keys}"

puts "\n3. Testing User Model:"
puts "- User has accessible_items method: #{User.instance_methods.include?(:accessible_items)}"
puts "- User has accessible_requests method: #{User.instance_methods.include?(:accessible_requests)}"

puts "\n4. Testing Database Schema:"
puts "- Items table has project_id: #{Item.column_names.include?('project_id')}"
puts "- ProcurementRequests table has project_id: #{ProcurementRequest.column_names.include?('project_id')}"
puts "- Items table has vendor_name: #{Item.column_names.include?('vendor_name')}"

puts "\n5. Testing Default Status:"
puts "- Default status for new ProcurementRequest:"
pr = ProcurementRequest.new
puts "  Status: #{pr.status}"

puts "\nImplementation test completed!"
