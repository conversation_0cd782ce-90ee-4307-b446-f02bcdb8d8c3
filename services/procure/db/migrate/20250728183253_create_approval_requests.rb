class CreateApprovalRequests < ActiveRecord::Migration[8.0]
  def change
    create_table :approval_requests do |t|
      t.string :workflow_id, null: false
      t.string :workflow_name, null: false
      t.string :requestor_id, null: false
      t.index :requestor_id
      t.string :project_id, null: true
      t.index :project_id
      t.references :approvable, polymorphic: true, null: false
      t.integer :status, null: false, default: 0
      t.json :steps_data

      t.timestamps
    end
  end
end
