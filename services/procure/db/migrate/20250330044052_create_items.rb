class CreateItems < ActiveRecord::Migration[8.0]
  def change
    create_table :items, id: :uuid do |t|
      t.string  :name, null: false
      t.text    :description
      t.string  :category, null: false
      t.decimal :approx_price, precision: 10, scale: 2, null: false
      t.string  :vendor_name
      t.uuid    :created_by_id, null: false

      t.timestamps
    end

    add_index :items, :category
    add_index :items, :created_by_id
  end
end
