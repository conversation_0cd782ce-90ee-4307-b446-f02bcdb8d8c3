class CreateProcurementRequests < ActiveRecord::Migration[8.0]
  def change
    create_table :procurement_requests, id: :uuid do |t|
      t.uuid    :item_id, null: false
      t.uuid    :requester_id, null: false
      t.string  :status, null: false, default: "pending"
      t.integer :quantity, null: false, default: 1
      t.text    :note
      t.datetime :submitted_at
      t.datetime :decided_at

      t.timestamps
    end

    add_index :procurement_requests, :status
    add_index :procurement_requests, :requester_id
    add_index :procurement_requests, :item_id
  end
end
