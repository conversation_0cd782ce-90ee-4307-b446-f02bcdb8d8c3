class UpdateItemsAndProcurementRequestsSchema < ActiveRecord::Migration[8.0]
  def change
    # Remove vendor_name from items (no suppliers in system)
    remove_column :items, :vendor_name, :string

    # Update default status from "pending" to "submitted"
    change_column_default :procurement_requests, :status, "submitted"

    # Update existing "pending" records to "submitted"
    reversible do |dir|
      dir.up do
        execute "UPDATE procurement_requests SET status = 'submitted' WHERE status = 'pending'"
      end
      dir.down do
        execute "UPDATE procurement_requests SET status = 'pending' WHERE status = 'submitted'"
      end
    end
  end
end
