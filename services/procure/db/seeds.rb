# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

puts "🌱 Seeding AtharProcure database..."

# Sample project IDs (these would normally come from the Core service)
# Using fixed UUIDs for consistency across environments
SAMPLE_PROJECTS = {
  "Alpha Project" => "01234567-89ab-cdef-0123-456789abcdef",
  "Beta Project" => "11234567-89ab-cdef-0123-456789abcdef",
  "Gamma Project" => "21234567-89ab-cdef-0123-456789abcdef",
  "Delta Project" => "31234567-89ab-cdef-0123-456789abcdef"
}.freeze

# Sample user IDs (these would normally come from the Core service via AtharAuth)
# Using fixed UUIDs for consistency across environments
SAMPLE_USERS = {
  "<PERSON> (PM Alpha)" => "a1234567-89ab-cdef-0123-456789abcdef",
  "<PERSON> (PM Beta)" => "b1234567-89ab-cdef-0123-45678<PERSON>ab<PERSON>def",
  "<PERSON> (Employee Alpha)" => "c1234567-89ab-cdef-0123-456789abcdef",
  "Alice Brown (Employee Beta)" => "d1234567-89ab-cdef-0123-456789abcdef",
  "Mike Johnson (Finance Manager)" => "e1234567-89ab-cdef-0123-456789abcdef",
  "Sarah Davis (HR Manager)" => "f1234567-89ab-cdef-0123-456789abcdef"
}.freeze

puts "📦 Creating sample items..."

# Electronics category items
# Create items across different categories and projects
items_data = [
  # Electronics category items
  {
    name: "Dell XPS 13 Laptop",
    description: "13-inch ultrabook with Intel Core i7, 16GB RAM, 512GB SSD",
    category: "Electronics",
    approx_price: 1299.99,
    project_id: SAMPLE_PROJECTS["Alpha Project"],
    created_by_id: SAMPLE_USERS["John Doe (PM Alpha)"]
  },
  {
    name: "MacBook Pro 14-inch",
    description: "Apple MacBook Pro with M3 chip, 18GB RAM, 512GB SSD",
    category: "Electronics",
    approx_price: 1999.99,
    project_id: SAMPLE_PROJECTS["Beta Project"],
    created_by_id: SAMPLE_USERS["Jane Smith (PM Beta)"]
  },
  {
    name: "iPhone 15 Pro",
    description: "Latest iPhone with 256GB storage, Titanium finish",
    category: "Electronics",
    approx_price: 999.99,
    project_id: SAMPLE_PROJECTS["Alpha Project"],
    created_by_id: SAMPLE_USERS["John Doe (PM Alpha)"]
  },
  {
    name: "Samsung 27-inch 4K Monitor",
    description: "4K UHD monitor with USB-C connectivity and height adjustment",
    category: "Electronics",
    approx_price: 349.99,
    project_id: SAMPLE_PROJECTS["Gamma Project"],
    created_by_id: SAMPLE_USERS["John Doe (PM Alpha)"]
  },
  {
    name: "Logitech MX Master 3S Mouse",
    description: "Wireless mouse with precision tracking and customizable buttons",
    category: "Electronics",
    approx_price: 99.99,
    project_id: SAMPLE_PROJECTS["Beta Project"],
    created_by_id: SAMPLE_USERS["Jane Smith (PM Beta)"]
  },

  # Office Furniture category items
  {
    name: "Herman Miller Aeron Chair",
    description: "Ergonomic office chair with lumbar support and adjustable height",
    category: "Office Furniture",
    approx_price: 1395.00,
    project_id: SAMPLE_PROJECTS["Alpha Project"],
    created_by_id: SAMPLE_USERS["John Doe (PM Alpha)"]
  },
  {
    name: "Standing Desk Converter",
    description: "Height-adjustable desk converter for sit-stand workstation",
    category: "Office Furniture",
    approx_price: 299.99,
    project_id: SAMPLE_PROJECTS["Beta Project"],
    created_by_id: SAMPLE_USERS["Jane Smith (PM Beta)"]
  },
  {
    name: "Conference Table (8-person)",
    description: "Large conference table with cable management and modern design",
    category: "Office Furniture",
    approx_price: 1899.99,
    project_id: SAMPLE_PROJECTS["Gamma Project"],
    created_by_id: SAMPLE_USERS["John Doe (PM Alpha)"]
  },

  # Office Supplies category items
  {
    name: "Whiteboard Markers Set",
    description: "Set of 12 dry-erase markers in assorted colors",
    category: "Office Supplies",
    approx_price: 24.99,
    project_id: SAMPLE_PROJECTS["Alpha Project"],
    created_by_id: SAMPLE_USERS["John Doe (PM Alpha)"]
  },
  {
    name: "Printer Paper (Case)",
    description: "Case of 10 reams of 8.5x11 white copy paper",
    category: "Office Supplies",
    approx_price: 49.99,
    project_id: SAMPLE_PROJECTS["Beta Project"],
    created_by_id: SAMPLE_USERS["Jane Smith (PM Beta)"]
  },
  {
    name: "Notebook Set (5-pack)",
    description: "Professional notebooks with lined pages and hard covers",
    category: "Office Supplies",
    approx_price: 39.99,
    project_id: SAMPLE_PROJECTS["Delta Project"],
    created_by_id: SAMPLE_USERS["John Doe (PM Alpha)"]
  },

  # Software category items
  {
    name: "Adobe Creative Suite License",
    description: "Annual license for Adobe Creative Cloud All Apps",
    category: "Software",
    approx_price: 659.88,
    project_id: SAMPLE_PROJECTS["Alpha Project"],
    created_by_id: SAMPLE_USERS["John Doe (PM Alpha)"]
  },
  {
    name: "Microsoft Office 365 Business",
    description: "Annual subscription for Office 365 Business Premium",
    category: "Software",
    approx_price: 264.00,
    project_id: SAMPLE_PROJECTS["Beta Project"],
    created_by_id: SAMPLE_USERS["Jane Smith (PM Beta)"]
  },
  {
    name: "Slack Pro Plan",
    description: "Annual Slack Pro plan for team communication",
    category: "Software",
    approx_price: 96.00,
    project_id: SAMPLE_PROJECTS["Gamma Project"],
    created_by_id: SAMPLE_USERS["John Doe (PM Alpha)"]
  }
]

# Create items with find_or_create_by to ensure idempotency
items_data.each do |item_attrs|
  item = Item.find_or_create_by(
    name: item_attrs[:name],
    project_id: item_attrs[:project_id]
  ) do |new_item|
    new_item.assign_attributes(item_attrs)
  end

  if item.persisted?
    puts "  ✅ Created/Found item: #{item.name} (#{item.category}) - Project: #{SAMPLE_PROJECTS.key(item.project_id)}"
  else
    puts "  ❌ Failed to create item: #{item_attrs[:name]} - Errors: #{item.errors.full_messages.join(', ')}"
  end
end

puts "\n📋 Creating sample procurement requests..."

# Get created items for requests
laptop_alpha = Item.find_by(name: "Dell XPS 13 Laptop", project_id: SAMPLE_PROJECTS["Alpha Project"])
macbook_beta = Item.find_by(name: "MacBook Pro 14-inch", project_id: SAMPLE_PROJECTS["Beta Project"])
chair_alpha = Item.find_by(name: "Herman Miller Aeron Chair", project_id: SAMPLE_PROJECTS["Alpha Project"])
monitor_gamma = Item.find_by(name: "Samsung 27-inch 4K Monitor", project_id: SAMPLE_PROJECTS["Gamma Project"])
adobe_alpha = Item.find_by(name: "Adobe Creative Suite License", project_id: SAMPLE_PROJECTS["Alpha Project"])
desk_beta = Item.find_by(name: "Standing Desk Converter", project_id: SAMPLE_PROJECTS["Beta Project"])
markers_alpha = Item.find_by(name: "Whiteboard Markers Set", project_id: SAMPLE_PROJECTS["Alpha Project"])

# Sample procurement requests with various statuses
procurement_requests_data = [
  # Submitted requests (just created, waiting for approval workflow)
  {
    item: laptop_alpha,
    requester_id: SAMPLE_USERS["Bob Wilson (Employee Alpha)"],
    project_id: SAMPLE_PROJECTS["Alpha Project"],
    status: "submitted",
    quantity: 1,
    note: "Need new laptop for development work. Current one is too slow.",
    submitted_at: 2.days.ago
  },
  {
    item: macbook_beta,
    requester_id: SAMPLE_USERS["Alice Brown (Employee Beta)"],
    project_id: SAMPLE_PROJECTS["Beta Project"],
    status: "submitted",
    quantity: 1,
    note: "MacBook needed for iOS development tasks.",
    submitted_at: 1.day.ago
  },

  # Under review requests (approval workflow in progress)
  {
    item: chair_alpha,
    requester_id: SAMPLE_USERS["Bob Wilson (Employee Alpha)"],
    project_id: SAMPLE_PROJECTS["Alpha Project"],
    status: "under_review",
    quantity: 1,
    note: "Ergonomic chair needed due to back issues. Doctor recommended.",
    submitted_at: 5.days.ago
  },
  {
    item: monitor_gamma,
    requester_id: SAMPLE_USERS["John Doe (PM Alpha)"],
    project_id: SAMPLE_PROJECTS["Gamma Project"],
    status: "under_review",
    quantity: 2,
    note: "Additional monitors for the new conference room setup.",
    submitted_at: 3.days.ago
  },

  # Approved requests (ready for external procurement)
  {
    item: adobe_alpha,
    requester_id: SAMPLE_USERS["Bob Wilson (Employee Alpha)"],
    project_id: SAMPLE_PROJECTS["Alpha Project"],
    status: "approved",
    quantity: 1,
    note: "Adobe license for graphic design work on marketing materials.",
    submitted_at: 10.days.ago,
    decided_at: 7.days.ago
  },
  {
    item: desk_beta,
    requester_id: SAMPLE_USERS["Alice Brown (Employee Beta)"],
    project_id: SAMPLE_PROJECTS["Beta Project"],
    status: "approved",
    quantity: 1,
    note: "Standing desk converter for better health and productivity.",
    submitted_at: 8.days.ago,
    decided_at: 5.days.ago
  },

  # Processing requests (external procurement in progress)
  {
    item: markers_alpha,
    requester_id: SAMPLE_USERS["John Doe (PM Alpha)"],
    project_id: SAMPLE_PROJECTS["Alpha Project"],
    status: "processing",
    quantity: 3,
    note: "Whiteboard markers for team brainstorming sessions.",
    submitted_at: 15.days.ago,
    decided_at: 12.days.ago
  },

  # Delivered requests (completed)
  {
    item: Item.find_by(name: "Printer Paper (Case)", project_id: SAMPLE_PROJECTS["Beta Project"]),
    requester_id: SAMPLE_USERS["Jane Smith (PM Beta)"],
    project_id: SAMPLE_PROJECTS["Beta Project"],
    status: "delivered",
    quantity: 2,
    note: "Office supplies for the team. Running low on printer paper.",
    submitted_at: 20.days.ago,
    decided_at: 17.days.ago
  },

  # Rejected request (example of denied request)
  {
    item: Item.find_by(name: "iPhone 15 Pro", project_id: SAMPLE_PROJECTS["Alpha Project"]),
    requester_id: SAMPLE_USERS["Bob Wilson (Employee Alpha)"],
    project_id: SAMPLE_PROJECTS["Alpha Project"],
    status: "rejected",
    quantity: 1,
    note: "Personal phone upgrade request.",
    submitted_at: 12.days.ago,
    decided_at: 9.days.ago
  },

  # Cancelled request (user cancelled their own request)
  {
    item: Item.find_by(name: "Conference Table (8-person)", project_id: SAMPLE_PROJECTS["Gamma Project"]),
    requester_id: SAMPLE_USERS["John Doe (PM Alpha)"],
    project_id: SAMPLE_PROJECTS["Gamma Project"],
    status: "cancelled",
    quantity: 1,
    note: "Conference table for meeting room. Actually, we found a used one instead.",
    submitted_at: 6.days.ago
  }
]

# Create procurement requests using direct SQL to avoid association validation issues
procurement_requests_data.each do |request_attrs|
  next unless request_attrs[:item] # Skip if item not found

  begin
    # Check if request already exists
    existing_request = ProcurementRequest.find_by(
      item_id: request_attrs[:item].id,
      requester_id: request_attrs[:requester_id],
      project_id: request_attrs[:project_id],
      note: request_attrs[:note]
    )

    if existing_request
      project_name = SAMPLE_PROJECTS.key(existing_request.project_id)
      requester_name = SAMPLE_USERS.key(existing_request.requester_id)
      puts "  ✅ Found existing request: #{existing_request.item.name} (#{existing_request.status}) - #{requester_name} in #{project_name}"
    else
      # Create using direct SQL to bypass association validation
      request_id = SecureRandom.uuid

      ActiveRecord::Base.connection.execute(<<~SQL)
        INSERT INTO procurement_requests (
          id, item_id, requester_id, project_id, status, quantity, note,
          submitted_at, decided_at, created_at, updated_at
        ) VALUES (
          '#{request_id}',
          '#{request_attrs[:item].id}',
          '#{request_attrs[:requester_id]}',
          '#{request_attrs[:project_id]}',
          '#{request_attrs[:status]}',
          #{request_attrs[:quantity]},
          '#{request_attrs[:note].gsub("'", "''")}',
          #{request_attrs[:submitted_at] ? "'#{request_attrs[:submitted_at].strftime('%Y-%m-%d %H:%M:%S')}'" : 'NULL'},
          #{request_attrs[:decided_at] ? "'#{request_attrs[:decided_at].strftime('%Y-%m-%d %H:%M:%S')}'" : 'NULL'},
          '#{Time.current.strftime('%Y-%m-%d %H:%M:%S')}',
          '#{Time.current.strftime('%Y-%m-%d %H:%M:%S')}'
        )
      SQL

      project_name = SAMPLE_PROJECTS.key(request_attrs[:project_id])
      requester_name = SAMPLE_USERS.key(request_attrs[:requester_id])
      puts "  ✅ Created request: #{request_attrs[:item].name} (#{request_attrs[:status]}) - #{requester_name} in #{project_name}"
    end
  rescue => e
    puts "  ❌ Error creating request for #{request_attrs[:item]&.name}: #{e.message}"
  end
end

puts "\n📊 Seeding Summary:"
puts "  📦 Items created: #{Item.count}"
puts "  📋 Procurement requests created: #{ProcurementRequest.count}"
puts "  🏢 Projects represented: #{SAMPLE_PROJECTS.keys.join(', ')}"
puts "  👥 Users represented: #{SAMPLE_USERS.keys.join(', ')}"

puts "\n📈 Status Distribution:"
ProcurementRequest.group(:status).count.each do |status, count|
  puts "  #{status.capitalize}: #{count} requests"
end

puts "\n🏷️ Category Distribution:"
Item.joins(:procurement_requests).group(:category).count.each do |category, count|
  puts "  #{category}: #{count} requests"
end

puts "\n🎉 AtharProcure database seeding completed successfully!"
puts "\n💡 You can now:"
puts "  • Browse items across different projects and categories"
puts "  • View procurement requests in various approval stages"
puts "  • Test the approval workflow with sample data"
puts "  • Explore project-based multi-tenancy features"
puts "  • Test role-based access control with different user types"
